import { useQuery } from '@tanstack/react-query';
import type { OrderHistoryDetailsType } from '@/libs/orders/types';
import { get } from '@/libs/utils/api';
import { queryKeys } from '@/libs/query/queryClient';

interface useOrderDetailsProps {
  id: string;
}

const fetchOrderDetails = async (
  id: string,
): Promise<OrderHistoryDetailsType> => {
  return get<OrderHistoryDetailsType>({
    url: `/orders/${id}`,
  });
};

export const useOrderDetails = ({ id }: useOrderDetailsProps) => {
  const {
    data: order,
    isLoading,
    isError: errorOnLoading,
  } = useQuery({
    queryKey: queryKeys.orders.details(id),
    queryFn: () => fetchOrderDetails(id),
  });

  return {
    order: order || null,
    errorOnLoading,
    isLoading,
  };
};
